---
## 디지털 카드 플립 명함 서비스 (MVP) - 제품 요구사항 정의 (PRD)

### 1. 개요 (Introduction)

본 문서는 '디지털 카드 플립 명함 서비스'의 초기 MVP(Minimum Viable Product)에 대한 제품 요구사항을 정의합니다. 본 서비스는 사용자가 기존의 명함 이미지를 업로드하면, 이를 멋진 카드 플립 애니메이션이 적용된 디지털 명함으로 변환하고 고유한 웹 주소(URL)를 통해 쉽게 공유할 수 있도록 하는 데 중점을 둡니다.

**핵심 가치 제안:**
* **시각적 매력:** 일반적인 디지털 명함과는 차별화된 카드 플립 애니메이션으로 특별한 인상을 선사합니다.
* **간편한 변환:** 복잡한 디자인 과정 없이, 기존 명함 이미지를 활용하여 디지털 명함으로 손쉽게 변환합니다.
* **손쉬운 공유:** 고유 URL과 QR 코드를 통해 언제 어디서든 빠르게 명함을 공유할 수 있습니다.
* **전문성 강화:** 자신만의 디지털 명함 페이지를 통해 개인 브랜딩 및 전문성을 향상시킵니다.
---

### 2. 목표 (Goals)

- 핵심 기능인 '카드 플립 애니메이션이 적용된 명함 생성 및 공유'의 시장 반응을 빠르게 검증합니다.
- 사용자 유치를 위한 최소한의 허들을 제공하여 높은 가입 및 명함 생성 전환율을 확보합니다.
- 개발 자원을 핵심 기능에 집중하여 빠른 MVP 출시를 달성합니다.

---

### 3. 사용자 스토리 (User Stories)

사용자 관점에서 핵심 기능을 정의합니다.

- **로그인/회원가입:**
  - **AS A** 사용자 **I WANT TO** 구글 소셜 계정으로 **간편하게 회원가입 및 로그인**하여 서비스를 이용하고 싶다.
- **다국어 지원:**
  - **AS A** 사용자 **I WANT TO** 한국어, 일본어, 영어 중 **원하는 언어로 서비스를 이용**하고 싶다.
- **명함 이미지 업로드:**
  - **AS A** 사용자 **I WANT TO** 내가 가진 명함의 **앞면과 뒷면 이미지를 쉽게 업로드**하여 디지털 명함의 기본 배경으로 사용하고 싶다.
  - **AS A** 사용자 **I WANT TO** 가로형/세로형 명함 중 나의 명함 형태에 맞는 **명함 타입을 선택**하여 올바른 비율로 표시되게 하고 싶다.
  - **AS A** 사용자 **I WANT TO** 업로드한 이미지가 웹에서 보기 좋게 **자동으로 최적화(리사이징, 포맷 변환)되어 처리**되기를 원한다.
- **디지털 명함 생성 및 미리보기:**
  - **AS A** 사용자 **I WANT TO** 업로드한 이미지로 생성될 **명함의 실시간 미리보기**를 확인하고 싶다.
  - **AS A** 사용자 **I WANT TO** 미리보기에서 명함 앞면과 뒷면이 **카드 플립 애니메이션으로 전환되는 것을 확인**하고 싶다.
- **명함 저장 및 고유 URL 생성:**
  - **AS A** 사용자 **I WANT TO** 생성한 디지털 명함을 **저장**하고 나만의 **고유한 URL**을 부여받고 싶다.
- **명함 공유:**
  - **AS A** 사용자 **I WANT TO** 생성된 명함의 **URL을 쉽게 복사**하여 필요한 사람에게 전달하고 싶다.
  - **AS A** 사용자 **I WANT TO** 내 명함 URL을 담은 **QR 코드를 생성하고 다운로드**하여 다양한 방식으로 공유하고 싶다.
- **내 명함 관리 (대시보드):**
  - **AS A** 사용자 **I WANT TO** 로그인 후 내가 만든 **디지털 명함 목록을 한눈에 확인**하고 싶다.
  - **AS A** 사용자 **I WANT TO** 내 명함 목록에서 각 명함의 **URL 복사, QR 코드 다운로드, 삭제** 등의 관리를 하고 싶다.

---

### 4. 기능 요구사항 (Functional Requirements)

MVP의 핵심 기능 목록입니다.

- **FR1. 회원가입 및 로그인**
  - FR1.1. 카카오톡, 구글, 네이버 OAuth 2.0을 통한 회원가입 및 로그인 지원.
  - FR1.2. 사용자가 첫 로그인 시(DB에 정보가 없을 경우) 자동으로 회원가입 처리.
  - FR1.3. 로그인 후 사용자를 대시보드 페이지로 리다이렉트.
- **FR2. 명함 이미지 업로드**
  - FR2.1. 명함 앞면/뒷면 이미지를 각각 업로드할 수 있는 UI 제공 (드래그 앤 드롭 지원).
  - FR2.2. 사용자가 업로드 전 가로형/세로형 명함 타입을 선택할 수 있는 옵션 제공.
  - FR2.3. 업로드된 이미지 파일(JPG, PNG 등)을 서버에서 처리하여 웹 최적화 포맷(WebP 권장)으로 변환 및 리사이징.
  - FR2.5. 이미지 업로드 중 진행 상태 표시 (예: 로딩 스피너).
- **FR3. 디지털 명함 생성 및 저장**
  - FR3.1. 업로드된 명함 이미지를 기반으로 디지털 명함의 실시간 미리보기 제공.
  - FR3.2. 명함 타입(가로/세로)에 따라 미리보기의 비율을 자동으로 조정.
  - FR3.3. 사용자가 미리보기에서 카드 플립 애니메이션을 확인할 수 있도록 상호작용 가능한 데모 제공.
  - FR3.4. "명함 만들기" 또는 "저장하기" 버튼 클릭 시 명함 데이터를 DB에 저장.
  - FR3.5. 저장 시 해당 명함에 대한 고유 ID(UUID) 자동 생성.
- **FR4. 명함 뷰어**
  - FR4.1. 고유 ID를 포함하는 URL(`yourdomain.com/card/[명함ID]`)을 통해 명함 페이지 접근 가능.
  - FR4.2. 명함 앞면/뒷면 이미지를 사용하여 디지털 명함을 렌더링.
  - FR4.3. 명함을 클릭하거나 스와이프(모바일) 시 부드러운 카드 플립 애니메이션 작동.
  - FR4.4. 명함 애니메이션은 모바일 화면에서 잘 작동하도록 최적화.
- **FR5. 명함 공유**
  - FR5.1. 명함 뷰어 페이지에서 명함 URL을 복사할 수 있는 버튼 제공.
  - FR5.2. 명함 URL을 기반으로 QR 코드를 자동 생성하고, 사용자가 QR 코드를 이미지 파일로 다운로드할 수 있는 기능 제공.
- **FR6. 대시보드**
  - FR6.1. 로그인한 사용자가 자신이 생성한 디지털 명함 목록을 볼 수 있는 UI 제공.
  - FR6.2. 각 명함 목록 항목에 명함 미리보기, 고유 URL 표시.
  - FR6.3. 각 명함에 대한 URL 복사, QR 코드 다운로드, 명함 삭제 기능 제공.

---

### 5. 비기능 요구사항 (Non-Functional Requirements)

- **성능:**
  - 페이지 로딩 시간: 모든 핵심 페이지는 3초 이내에 로드되어야 합니다.
  - 애니메이션 성능: 카드 플립 애니메이션은 모든 지원 기기에서 부드럽게(60 FPS) 작동해야 합니다.
  - 이미지 처리 속도: 명함 이미지 업로드 및 처리는 5초 이내에 완료되어야 합니다.
- **보안:**
  - 사용자 인증: Supabase Auth를 통한 안전한 OAuth 인증 구현.
  - 데이터 보안: 사용자 명함 데이터(이미지 URL, 고유 ID)는 암호화되어 안전하게 저장.
- **확장성:**
  - 사용자 증가에 따른 시스템 확장성 고려 (Next.js & Vercel, Supabase의 스케일링 이점 활용).
- **사용성:**
  - 모바일 최적화: 모든 페이지는 모바일 기기에서 원활하게 작동하고 보기 좋게 표시되어야 합니다.
  - 직관적인 UI/UX: 사용자가 쉽게 서비스를 이해하고 이용할 수 있도록 명확한 디자인과 안내 제공.
- **접근성:**
  - 웹 표준 및 기본 웹 접근성 가이드라인 준수.
- **비용 효율성:**
  - Supabase의 무료 플랜을 활용.

---

### 6. 기술 스택 (Technology Stack)

- **프론트엔드/백엔드 프레임워크:** Next.js (with API Routes)
- **데이터베이스 및 인증, 스토리지:** Supabase
- **호스팅:** Vercel
- **이미지 처리:**
  - **이미지 저장:** Supabase Storage (또는 Vercel Blob, AWS S3 등)
  - **자동 크롭/리사이징:** Sharp (Node.js) 또는 서버리스 함수를 통한 구현 (선택 사항)
- **애니메이션:** CSS `transform` & `transition` 또는 `framer-motion`
- **QR 코드 생성:** `qrcode.react`

---

### 7. 향후 고려사항 (Out of Scope for MVP)

다음 기능들은 초기 MVP 범위에는 포함되지 않으며, 시장 반응 및 사용자 피드백에 따라 추후 고려합니다.

- 복잡한 텍스트 오버레이 편집 (폰트 종류/크기/색상 자유 선택, 레이어 순서 조절 등).
- 명함 이미지에 대한 고도화된 자동 보정 (완벽한 누끼 따기, 화이트 밸런스 조정 등).
- 명함 디자인 템플릿 제공.
- 결제 시스템 연동 (유료 구독 모델).
- 명함 통계 및 분석 기능 (조회수, 공유 횟수 등).
- 기업용 관리자 대시보드 및 일괄 명함 생성 기능.
- 사용자 프로필 페이지 및 추가 정보 입력.

---
