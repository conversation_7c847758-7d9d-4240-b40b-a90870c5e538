- **Phase 1 (MVP - 현재 완료/진행 중인 목표):**

  - FR1. 회원가입 및 로그인: 구글 OAuth 2.0 기반으로, 별도 정보 입력 없이 간편하게 로그인 및 자동 회원가입. 로그인 후 사용자를 대시보드 페이지로 리다이렉트. **(완료)**
  - FR2. 명함 이미지 업로드: 명함 앞면/뒷면 이미지를 각각 업로드할 수 있는 UI 제공 (드래그 앤 드롭 지원). 사용자가 업로드 전 가로형/세로형 명함 타입을 선택하는 옵션 제공. 업로드된 이미지 파일을 서버에서 처리하여 웹 최적화 포맷(WebP 권장)으로 변환 및 리사이징. 명확한 촬영/스캔 가이드라인을 UI에 명시하고, 이미지 품질이 낮을 경우 재업로드를 유도하는 경고 메시지 표시. 이미지 업로드 중 진행 상태 표시. **(완료)**
  - FR3. 디지털 명함 생성 및 저장: 업로드된 명함 이미지를 기반으로 디지털 명함의 실시간 미리보기 제공. 명함 타입에 따라 미리보기 비율 자동 조정. 미리보기에서 카드 플립 애니메이션 확인 가능한 데모 제공. "명함 만들기" 또는 "저장하기" 버튼 클릭 시 명함 데이터 DB에 저장 및 고유 ID(UUID) 자동 생성. **(완료)**
  - FR4. 명함 뷰어: 고유 ID를 포함하는 URL(`yourdomain.com/card/[명함ID]`)을 통해 명함 페이지 접근 가능. 명함 앞면/뒷면 이미지를 사용하여 디지털 명함 렌더링. 명함을 클릭하거나 스와이프(모바일) 시 부드러운 카드 플립 애니메이션 작동. 명함 애니메이션은 모바일 화면에서 잘 작동하도록 최적화. **(완료)**
  - FR5. 명함 공유: 명함 뷰어 페이지에서 명함 URL을 복사할 수 있는 버튼 제공. 명함 URL 기반 QR 코드를 자동 생성하고, 사용자가 QR 코드를 이미지 파일로 다운로드할 수 있는 기능 제공. **(완료)**
  - FR6. 내 명함 관리 (대시보드): 로그인 후 자신이 생성한 디지털 명함 목록 확인 UI 제공. 각 명함에 대한 미리보기, 고유 URL 표시. URL 복사, QR 코드 다운로드, 명함 삭제 기능 제공. **(완료)**

- **Phase 2a: 고 우선순위 (즉각적인 사용자 가치 증대 및 핵심 경험 완성)**

  - **FR7. OCR 기반 연락처 정보 추출 및 자동 인터랙티브 존 생성 (OCR + Auto Interactive Zones):** 명함 이미지 등록 시 OpenAI Vision API를 통해 연락처 정보(이름, 직책, 회사명, 전화번호, 이메일, 웹사이트, 주소)를 자동 추출하고, 추출된 정보를 기반으로 클릭 가능한 인터랙티브 존을 자동 생성. 사용자는 OCR 결과를 검토하고 편집할 수 있으며, 최종 저장 시 해당 정보들이 명함 뷰어에서 클릭 가능한 버튼으로 표시됨. **(완료 - OCR 처리 및 자동 존 생성, 수동 편집 UI는 미완성)**
  - **FR8. 사용자 정의 단축 URL 구현 (Custom Slugs):** `yourdomain.com/john.doe`와 같이 사용자가 직접 깔끔한 URL을 설정할 수 있는 기능. **(완료)**
  - **FR9. 디지털 명함 지갑 자동 저장 플로우:** QR 스캔/URL 접속 시, 로그인 상태라면 상대방의 명함을 '내 명함 지갑'에 자동으로 저장. **(미구현)**
  - **FR10. 명함 이미지 업데이트 기능 (Maintain URL):** 기존 명함의 URL을 유지한 채 새로운 앞/뒷면 이미지를 업로드하여 명함을 업데이트하고, 클릭 가능 영역 조정 기능 제공. **(부분 완료 - 기본 구조만 완성)**

- **Phase 2b: 중 우선순위 (사용자 만족도 및 수익화 기반 강화)**

  - **FR11. 명함 지갑 관리:** 로그인 후 자신이 저장한(받은) 디지털 명함 목록을 한눈에 확인하고 관리할 수 있는 UI 제공. 각 명함에 대한 미리보기, 저장된 명함의 정보 표시. 내 명함과 저장된 명함을 구분할 수 있는 기능 제공.
    - **FR11.1. 주소록에 저장 버튼 (vCard Download):** 명함 정보 vCard 파일로 다운로드 기능.
    - **FR11.2. 명함 지갑 검색 및 분류:** 저장된 명함을 검색, 분류(태그), 즐겨찾기 할 수 있는 기능 추가.
    - **FR11.3. 명함 닉네임 설정:** 저장된 명함에 사용자가 직접 닉네임을 설정/수정할 수 있는 기능.
  - **FR12. 명함 조회수 및 클릭 분석 제공:** 내 명함의 조회수 및 링크 클릭 통계 대시보드. **(완료 - Analytics API 구현, 대시보드 UI는 미완성)**

**6.2. 기술 스택:**

- **프론트엔드/백엔드 프레임워크:** Next.js
- **데이터베이스/인증/스토리지:** Supabase
- **호스팅:** Vercel
- **이미지 처리:** Sharp (Node.js), Supabase Storage
- **애니메이션:** CSS `transform` & `transition`, `framer-motion`
- **QR 코드 생성:** `qrcode.react`

---

### 6.3. 현재 구현 상태 상세 분석 (2024년 12월 기준)

**✅ 완전 구현된 기능:**

- **Phase 1 (FR1-FR6)**: 모든 MVP 기능 완료
- **FR7 OCR 처리**: OpenAI Vision API 기반 자동 정보 추출, 앞면/뒷면 동시 처리
- **FR7 자동 인터랙티브 존**: OCR 결과 기반 자동 클릭 가능 영역 생성
- **FR8 커스텀 URL**: 사용자 정의 단축 URL 완전 구현 (동적 라우팅, 슬러그 설정 UI, 실시간 검증)
- **FR12 Analytics 백엔드**: 조회수/클릭수 추적 API

**🚧 부분 구현된 기능:**

- **FR7 수동 편집**: OCR 결과 편집 UI는 있지만 인터랙티브 존 수동 편집 UI 미완성
- **FR10 명함 업데이트**: 기본 구조만 완성, 완전한 업데이트 플로우 필요
- **FR12 Analytics 프론트엔드**: 백엔드는 완성, 대시보드 UI 미완성

**❌ 미구현 기능:**

- **FR9 명함 지갑**: 전체 미구현
- **FR11 명함 지갑 관리**: 전체 미구현

**🔧 기술적 변경사항:**

- OCR 처리 방식: 임시 데이터베이스 저장 → 로컬 상태 관리로 변경
- 인터랙티브 존: 수동 영역 설정 → OCR 기반 자동 생성으로 변경
- 연락처 연결: 터치 기반 영역 클릭 → 버튼 기반 액션으로 변경

**🌐 다국어 지원 (2024년 12월 완료):**

- **완전 번역 완료**: 한국어, 일본어, 영어 3개 언어 지원
- **번역된 컴포넌트**:
  - 전역 헤더 (로고, 언어 선택, 계정 관리)
  - 홈페이지 (히어로 섹션, 기능 소개, 푸터)
  - 대시보드 (명함 목록, 액션 버튼, 통계)
  - 명함 생성 페이지 (폼 라벨, 버튼, 안내 메시지)
  - 명함 뷰어 (공유 옵션, 사용법 안내, 커스텀 URL 설정)
  - 명함 업데이트 모달 (OCR 정보, 버전 관리)
  - 모든 에러 메시지 및 확인 다이얼로그
- **언어 변경**: 바텀 시트 UI로 직관적인 언어 선택, 변경 시 즉시 새로고침으로 반영
- **번역 시스템**: useTranslation 훅 기반, 타입 안전성 보장

**🔗 URL 및 공유 기능 개선 (2024년 12월 완료):**

- **스마트 URL 생성**: 대시보드 카드에서 링크 복사/QR 코드 생성 시 커스텀 슬러그 우선 사용
  - 슬러그 있음: `domain.com/slug` 형태
  - 슬러그 없음: `domain.com/card/id` 형태
- **로고 네비게이션**: 헤더 로고 클릭 시 홈페이지(/)로 새로고침 이동

**👤 사용자 계정 관리 개선 (2024년 12월 완료):**

- **전역 헤더 계정 관리**: 번역 기능 우른쪽에 계정 관리 드롭다운 메뉴 추가
  - 로그아웃 기능
  - 회원탈퇴 기능 (확인 다이얼로그, 실제 구현은 TODO)
  - 외부 클릭 감지로 자동 메뉴 닫기
- **대시보드 UI 정리**: 우측 상단 로그아웃 버튼 제거 (전역 헤더로 이동)

---

### 7. 비기능 요구사항 (Non-Functional Requirements)

- **성능:** 페이지 로딩 시간(3초 이내), 애니메이션 성능(60 FPS), 이미지 처리 속도(5초 이내) 등 전반적인 시스템 성능 최적화 유지.
- **보안:** Supabase Auth를 통한 안전한 OAuth 인증 구현, 사용자 명함 데이터(이미지 URL, 고유 ID) 암호화 및 안전한 저장.
- **확장성:** 사용자 증가에 따른 시스템 확장성 고려 (Next.js & Vercel, Supabase의 스케일링 이점 활용).
- **사용성:** 모바일 최적화(모든 페이지), 직관적인 UI/UX 제공.
- **접근성:** 웹 표준 및 기본 웹 접근성 가이드라인 준수.
- **비용 효율성:** Supabase의 무료 플랜을 활용하고, 트래픽 증가에 따른 비용 효율적인 스케일링 고려.

---
