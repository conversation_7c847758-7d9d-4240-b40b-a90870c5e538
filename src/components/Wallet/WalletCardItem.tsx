'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import Image from 'next/image'
import { useTranslation } from '@/hooks/useTranslation'

interface WalletCard {
  id: string
  business_card_id: string
  nickname: string | null
  tags: string[]
  is_favorite: boolean
  notes: string | null
  saved_at: string
  business_cards: {
    id: string
    title: string
    front_image_url: string
    back_image_url: string | null
    card_type: 'horizontal' | 'vertical'
    custom_slug: string | null
    view_count: number
    created_at: string
    profiles: {
      full_name: string | null
      email: string
    }
    business_card_ocr_data?: {
      id: string
      extracted_text: string | null
      confidence_score: number | null
      language_detected: string | null
      raw_data: any
    }[]
  }
}

interface WalletCardItemProps {
  card: WalletCard
  onDelete: () => void
  onUpdate: (updates: Partial<WalletCard>) => void
}

export default function WalletCardItem({ card, onDelete, onUpdate }: WalletCardItemProps) {
  const { t } = useTranslation()
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)

  // 카드 URL 생성 (슬러그가 있으면 슬러그 URL, 없으면 기본 URL)
  const cardUrl = card.business_cards.custom_slug
    ? `${window.location.origin}/${card.business_cards.custom_slug}`
    : `${window.location.origin}/card/${card.business_cards.id}`

  const handleFavoriteToggle = (e: React.MouseEvent) => {
    e.preventDefault() // Link 클릭 방지
    e.stopPropagation()
    onUpdate({ is_favorite: !card.is_favorite })
  }

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.preventDefault() // Link 클릭 방지
    e.stopPropagation()
    setShowDeleteConfirm(true)
  }

  return (
    <>
      <motion.div
        className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all duration-200 relative group"
        whileHover={{ y: -4, scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        {/* 즐겨찾기 버튼 */}
        <button
          onClick={handleFavoriteToggle}
          className={`absolute top-2 right-2 z-10 p-1.5 rounded-full transition-all duration-200 ${
            card.is_favorite
              ? 'text-yellow-500 hover:text-yellow-600 bg-white/80 backdrop-blur-sm'
              : 'text-gray-400 hover:text-yellow-500 bg-white/60 backdrop-blur-sm opacity-0 group-hover:opacity-100'
          }`}
        >
          <svg className="w-4 h-4" fill={card.is_favorite ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
          </svg>
        </button>

        {/* 삭제 버튼 */}
        <button
          onClick={handleDeleteClick}
          className="absolute top-2 left-2 z-10 p-1.5 rounded-full transition-all duration-200 text-gray-400 hover:text-red-500 bg-white/60 backdrop-blur-sm opacity-0 group-hover:opacity-100"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        </button>

        {/* Card Preview - 전체 카드가 클릭 가능 */}
        <Link href={cardUrl} target="_blank" rel="noopener noreferrer" className="block">
          <div className="p-4">
            <div className={`
              relative mx-auto
              ${card.business_cards.card_type === 'horizontal'
                ? 'w-full aspect-[1.6/1] max-w-48'
                : 'w-full aspect-[1/1.6] max-w-32 mx-auto'
              }
            `}>
              <Image
                src={card.business_cards.front_image_url}
                alt={card.nickname || card.business_cards.title}
                fill
                className="object-cover rounded-lg shadow-sm"
                onError={(e) => {
                  console.error('Image load error:', card.business_cards.front_image_url);
                  e.currentTarget.style.display = 'none';
                }}
                sizes="(max-width: 768px) 200px, 250px"
              />

              {/* Double-sided indicator */}
              {card.business_cards.back_image_url && (
                <div className="absolute bottom-2 right-2">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100/90 text-purple-800 backdrop-blur-sm">
                    양면
                  </span>
                </div>
              )}
            </div>
          </div>
        </Link>

      </motion.div>

      {/* 삭제 확인 모달 */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-lg p-6 max-w-sm w-full"
          >
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {t('deleteFromWalletConfirm')}
            </h3>
            <p className="text-sm text-gray-500 mb-4">
              이 작업은 되돌릴 수 없습니다.
            </p>
            <div className="flex gap-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="flex-1 bg-gray-200 text-gray-800 px-4 py-2 rounded font-medium hover:bg-gray-300 transition-colors"
              >
                {t('cancel')}
              </button>
              <button
                onClick={() => {
                  onDelete()
                  setShowDeleteConfirm(false)
                }}
                className="flex-1 bg-red-600 text-white px-4 py-2 rounded font-medium hover:bg-red-700 transition-colors"
              >
                {t('delete')}
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </>
  )
}
