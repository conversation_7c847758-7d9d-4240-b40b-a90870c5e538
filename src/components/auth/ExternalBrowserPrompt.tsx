'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { detectBrowser } from '@/lib/browser-detection'
import { useAuth } from '@/contexts/AuthContext'

interface ExternalBrowserPromptProps {
  onClose?: () => void
}

export default function ExternalBrowserPrompt({ onClose }: ExternalBrowserPromptProps) {
  const [isVisible, setIsVisible] = useState(false)
  const { user } = useAuth()

  useEffect(() => {
    // 이미 로그인된 경우 표시하지 않음
    if (user) return

    const browserInfo = detectBrowser()
    
    // 인앱 브라우저에서만 표시
    if (browserInfo.isInApp) {
      setIsVisible(true)
    }
  }, [user])

  const handleOpenInChrome = () => {
    const currentUrl = window.location.href
    
    // Android Chrome 인텐트 URL
    const chromeIntent = `intent://${currentUrl.replace(/^https?:\/\//, '')}#Intent;scheme=https;package=com.android.chrome;end`
    
    try {
      // Chrome 앱으로 열기 시도
      window.location.href = chromeIntent
      
      // 잠시 후 일반 URL로 폴백 (Chrome이 설치되지 않은 경우)
      setTimeout(() => {
        window.open(currentUrl, '_blank')
      }, 1000)
    } catch (error) {
      // 실패 시 새 탭에서 열기
      window.open(currentUrl, '_blank')
    }
  }

  const handleCopyUrl = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href)
      alert('URL이 복사되었습니다. Chrome 브라우저에 붙여넣어 주세요.')
    } catch (error) {
      // 클립보드 API가 지원되지 않는 경우
      const textArea = document.createElement('textarea')
      textArea.value = window.location.href
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      alert('URL이 복사되었습니다. Chrome 브라우저에 붙여넣어 주세요.')
    }
  }

  const handleClose = () => {
    setIsVisible(false)
    onClose?.()
  }

  if (!isVisible || user) {
    return null
  }

  const browserInfo = detectBrowser()

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 50 }}
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
      >
        <motion.div
          initial={{ scale: 0.9 }}
          animate={{ scale: 1 }}
          exit={{ scale: 0.9 }}
          className="bg-white rounded-2xl p-6 max-w-sm w-full shadow-2xl"
        >
          {/* 헤더 */}
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-2">
              명함을 저장하려면
            </h3>
            <p className="text-gray-600 text-sm">
              {browserInfo.platform === 'kakao' ? '카카오톡' : 
               browserInfo.platform === 'naver' ? '네이버' : 
               browserInfo.platform === 'instagram' ? '인스타그램' : '현재 앱'}에서는 
              로그인이 제한됩니다
            </p>
          </div>

          {/* 안내 메시지 */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div className="flex items-start">
              <svg className="w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
              <div className="text-sm text-blue-800">
                <p className="font-medium mb-1">Chrome 브라우저에서 열어주세요</p>
                <p>Google 로그인 후 명함을 저장할 수 있습니다</p>
              </div>
            </div>
          </div>

          {/* 버튼들 */}
          <div className="space-y-3 mb-4">
            {/* Chrome에서 열기 */}
            <button
              onClick={handleOpenInChrome}
              className="w-full flex items-center justify-center p-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
            >
              <svg className="w-5 h-5 mr-3" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 0C8.21 0 4.831 1.757 2.632 4.501l3.953 6.848A5.454 5.454 0 0 1 12 6.545h10.691A12 12 0 0 0 12 0zM1.931 5.47A11.943 11.943 0 0 0 0 12c0 6.012 4.42 10.991 10.189 11.864l3.953-6.847a5.45 5.45 0 0 1-6.865-2.29L1.931 5.47zm2.25 12.989A11.935 11.935 0 0 0 12 24c6.624 0 12-5.376 12-12 0-2.09-.536-4.057-1.479-5.773l-4.4 7.622a5.454 5.454 0 0 1-1.618 1.618L4.181 18.459z"/>
              </svg>
              Chrome에서 열기
            </button>

            {/* URL 복사 */}
            <button
              onClick={handleCopyUrl}
              className="w-full flex items-center justify-center p-4 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium"
            >
              <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
              URL 복사하기
            </button>
          </div>

          {/* 닫기 버튼 */}
          <button
            onClick={handleClose}
            className="w-full py-3 text-gray-500 text-sm hover:text-gray-700 transition-colors"
          >
            나중에 하기
          </button>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}
