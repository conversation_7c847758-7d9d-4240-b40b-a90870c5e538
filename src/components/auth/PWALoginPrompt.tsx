'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { detectBrowser } from '@/lib/browser-detection'
import { useTranslation } from '@/hooks/useTranslation'
import { useAuth } from '@/contexts/AuthContext'

interface PWALoginPromptProps {
  onClose?: () => void
}

export default function PWALoginPrompt({ onClose }: PWALoginPromptProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [isInstallable, setIsInstallable] = useState(false)
  const [deferredPrompt, setDeferredPrompt] = useState<any>(null)
  const { user } = useAuth()
  const { t } = useTranslation()

  useEffect(() => {
    // 이미 로그인된 경우 표시하지 않음
    if (user) return

    const browserInfo = detectBrowser()
    
    // 인앱 브라우저에서만 표시
    if (browserInfo.isInApp) {
      setIsVisible(true)
    }

    // PWA 설치 가능 여부 감지
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault()
      setDeferredPrompt(e)
      setIsInstallable(true)
    }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    }
  }, [user])

  const handleInstallPWA = async () => {
    if (!deferredPrompt) {
      // PWA 설치 프롬프트가 없는 경우 수동 안내
      showManualInstallGuide()
      return
    }

    try {
      deferredPrompt.prompt()
      const { outcome } = await deferredPrompt.userChoice
      
      if (outcome === 'accepted') {
        console.log('PWA 설치 완료')
        setIsVisible(false)
        onClose?.()
      }
      
      setDeferredPrompt(null)
      setIsInstallable(false)
    } catch (error) {
      console.error('PWA 설치 실패:', error)
      showManualInstallGuide()
    }
  }

  const showManualInstallGuide = () => {
    const browserInfo = detectBrowser()
    let message = ''

    switch (browserInfo.platform) {
      case 'kakao':
        message = '카카오톡에서:\n1. 우측 상단 메뉴(⋯) 클릭\n2. "다른 브라우저에서 열기" 선택\n3. Chrome/Safari에서 열린 후 로그인'
        break
      case 'naver':
        message = '네이버 앱에서:\n1. 우측 상단 메뉴 클릭\n2. "외부 브라우저에서 열기" 선택\n3. Chrome/Safari에서 열린 후 로그인'
        break
      default:
        message = '현재 앱에서:\n1. 메뉴에서 "외부 브라우저에서 열기" 선택\n2. Chrome/Safari에서 열린 후 로그인'
    }

    alert(message)
  }

  const handleClose = () => {
    setIsVisible(false)
    onClose?.()
  }

  if (!isVisible || user) {
    return null
  }

  const browserInfo = detectBrowser()

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 50 }}
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
      >
        <motion.div
          initial={{ scale: 0.9 }}
          animate={{ scale: 1 }}
          exit={{ scale: 0.9 }}
          className="bg-white rounded-2xl p-6 max-w-sm w-full shadow-2xl"
        >
          {/* 헤더 */}
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-2">
              더 편한 사용을 위해
            </h3>
            <p className="text-gray-600 text-sm">
              {browserInfo.platform === 'kakao' ? '카카오톡' : 
               browserInfo.platform === 'naver' ? '네이버' : '현재 앱'}에서는 
              로그인이 제한됩니다
            </p>
          </div>

          {/* 옵션들 */}
          <div className="space-y-3 mb-6">
            {/* PWA 설치 옵션 */}
            {isInstallable && (
              <button
                onClick={handleInstallPWA}
                className="w-full flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors"
              >
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </div>
                  <div className="text-left">
                    <div className="font-semibold text-gray-900">앱 설치하기</div>
                    <div className="text-sm text-gray-600">빠르고 편리한 접근</div>
                  </div>
                </div>
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            )}

            {/* 외부 브라우저 옵션 */}
            <button
              onClick={showManualInstallGuide}
              className="w-full flex items-center justify-between p-4 bg-gray-50 border border-gray-200 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <div className="flex items-center">
                <div className="w-10 h-10 bg-gray-600 rounded-lg flex items-center justify-center mr-3">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </div>
                <div className="text-left">
                  <div className="font-semibold text-gray-900">외부 브라우저에서 열기</div>
                  <div className="text-sm text-gray-600">Chrome, Safari에서 사용</div>
                </div>
              </div>
              <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>

          {/* 닫기 버튼 */}
          <button
            onClick={handleClose}
            className="w-full py-3 text-gray-500 text-sm hover:text-gray-700 transition-colors"
          >
            나중에 하기
          </button>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}
