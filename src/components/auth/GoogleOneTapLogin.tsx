'use client'

import { useEffect, useRef, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'

// Google Identity Services 타입 정의
declare global {
  interface Window {
    google?: {
      accounts: {
        id: {
          initialize: (config: any) => void
          prompt: (callback?: (notification: any) => void) => void
          renderButton: (element: HTMLElement, config: any) => void
          disableAutoSelect: () => void
        }
      }
    }
  }
}

interface GoogleOneTapLoginProps {
  onSuccess?: () => void
  onError?: (error: string) => void
}

export default function GoogleOneTapLogin({ onSuccess, onError }: GoogleOneTapLoginProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { user } = useAuth()
  const buttonRef = useRef<HTMLDivElement>(null)
  const [isGoogleLoaded, setIsGoogleLoaded] = useState(false)

  // Google Identity Services 초기화
  useEffect(() => {
    const initializeGoogle = () => {
      if (window.google?.accounts?.id) {
        setIsGoogleLoaded(true)
        
        window.google.accounts.id.initialize({
          client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || process.env.AUTH_GOOGLE_ID,
          callback: handleCredentialResponse,
          auto_select: false,
          cancel_on_tap_outside: true,
        })

        // 버튼 렌더링
        if (buttonRef.current) {
          window.google.accounts.id.renderButton(buttonRef.current, {
            theme: 'outline',
            size: 'large',
            width: '100%',
            text: 'signin_with',
            shape: 'rectangular',
            logo_alignment: 'left',
          })
        }
      }
    }

    // Google 스크립트가 로드될 때까지 대기
    const checkGoogleLoaded = () => {
      if (window.google?.accounts?.id) {
        initializeGoogle()
      } else {
        setTimeout(checkGoogleLoaded, 100)
      }
    }

    checkGoogleLoaded()
  }, [])

  // Google 로그인 응답 처리
  const handleCredentialResponse = async (response: any) => {
    try {
      setIsLoading(true)
      setError(null)

      // ID 토큰을 서버로 전송하여 인증 처리
      const authResponse = await fetch('/api/auth/google-onetap', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          credential: response.credential,
        }),
      })

      if (!authResponse.ok) {
        throw new Error('인증 처리 중 오류가 발생했습니다.')
      }

      const result = await authResponse.json()
      
      if (result.success) {
        onSuccess?.()
        // 페이지 새로고침으로 세션 업데이트
        window.location.reload()
      } else {
        throw new Error(result.error || '로그인에 실패했습니다.')
      }
    } catch (err) {
      console.error('Google One Tap Error:', err)
      const errorMessage = err instanceof Error ? err.message : '로그인 중 오류가 발생했습니다.'
      setError(errorMessage)
      onError?.(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  // 수동 로그인 트리거
  const handleManualLogin = () => {
    if (window.google?.accounts?.id && isGoogleLoaded) {
      window.google.accounts.id.prompt()
    }
  }

  // 이미 로그인된 경우 렌더링하지 않음
  if (user) {
    return null
  }

  return (
    <div className="w-full space-y-4">
      {/* Google One Tap 버튼 */}
      <div ref={buttonRef} className="w-full" />
      
      {/* 로딩 상태 */}
      {isLoading && (
        <div className="flex items-center justify-center py-2">
          <div className="animate-spin h-5 w-5 border-2 border-blue-600 border-t-transparent rounded-full"></div>
          <span className="ml-2 text-sm text-gray-600">로그인 중...</span>
        </div>
      )}

      {/* 에러 메시지 */}
      {error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-sm text-red-700">{error}</p>
        </div>
      )}

      {/* 수동 로그인 버튼 (One Tap이 안 될 경우 대체) */}
      {isGoogleLoaded && (
        <button
          onClick={handleManualLogin}
          disabled={isLoading}
          className="w-full text-sm text-blue-600 hover:text-blue-700 disabled:opacity-50"
        >
          Google 계정으로 로그인
        </button>
      )}

      {/* 개발 환경 정보 */}
      {process.env.NODE_ENV === 'development' && (
        <div className="text-xs text-gray-500 text-center">
          🚀 Google One Tap (인앱 브라우저 호환)
        </div>
      )}
    </div>
  )
}
