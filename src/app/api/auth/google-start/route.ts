import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // 1. 환경 변수 확인
    const clientId = process.env.AUTH_GOOGLE_ID
    let baseUrl = process.env.NEXTAUTH_URL

    // Vercel 배포 환경에서는 VERCEL_URL 사용
    if (!baseUrl && process.env.VERCEL_URL) {
      baseUrl = `https://${process.env.VERCEL_URL}`
    }

    if (!clientId) {
      return NextResponse.json(
        { message: 'Google Client ID not configured' },
        { status: 500 }
      )
    }

    if (!baseUrl) {
      return NextResponse.json(
        { message: 'Base URL not configured' },
        { status: 500 }
      )
    }

    // 2. 리다이렉트 URI 구성
    const redirectUri = `${baseUrl}/api/auth/callback/google`
    
    // 3. Google OAuth 인증 URL 생성
    const scopes = 'openid profile email'
    const state = crypto.randomUUID() // CSRF 보호를 위한 state 파라미터
    
    const authUrl = new URL('https://accounts.google.com/o/oauth2/v2/auth')
    authUrl.searchParams.set('client_id', clientId)
    authUrl.searchParams.set('redirect_uri', redirectUri)
    authUrl.searchParams.set('response_type', 'code')
    authUrl.searchParams.set('scope', scopes)
    authUrl.searchParams.set('prompt', 'consent')
    authUrl.searchParams.set('access_type', 'offline')
    authUrl.searchParams.set('state', state)

    // 4. 사용자를 Google 인증 페이지로 리다이렉션
    return NextResponse.redirect(authUrl.toString())

  } catch (error) {
    console.error('Error initiating Google login from API Route:', error)
    return NextResponse.json(
      { 
        message: 'Failed to initiate Google login', 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}
