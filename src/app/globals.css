@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Safe Area 지원 */
.safe-area-pb {
  padding-bottom: env(safe-area-inset-bottom);
}

/* 모바일 최적화 */
@media (max-width: 640px) {
  /* 터치 영역 최적화 */
  button, a {
    min-height: 44px;
  }

  /* 텍스트 가독성 개선 */
  body {
    -webkit-text-size-adjust: 100%;
    text-size-adjust: 100%;
  }

  /* 스크롤 성능 개선 */
  * {
    -webkit-overflow-scrolling: touch;
  }
}

/* 토스 스타일 애니메이션 */
.toss-bounce {
  animation: toss-bounce 0.3s ease-out;
}

@keyframes toss-bounce {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}


